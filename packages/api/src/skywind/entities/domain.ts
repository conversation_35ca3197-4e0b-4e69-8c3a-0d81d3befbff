export enum DomainStatus {
    ACTIVE = "active",
    SUSPENDED = "suspended"
}

export enum StaticDomainType {
    STATIC = "static",
    LOBBY = "lobby",
    LIVE_STREAMING = "live-streaming",
    EHUB = "ehub"
}

export interface Domain {
    id?: number;
    domain: string;
    description?: string;
    provider?: string;
    status: DomainStatus;
    expiryDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

// Backward compatibility interface for old UBO version
export interface DomainBackwardCompatible extends Domain {
    name: string; // maps to domain field
    isActive: boolean; // maps to status field (active = true, suspended = false)
}

export type CreateDomain<T extends Domain> = Omit<T, "status"> & Partial<Pick<T, "status">>;

export interface DynamicDomain extends Domain {
    environment: string;
}

export type CreateDynamicDomain = Omit<DynamicDomain, "status"> & Partial<Pick<DynamicDomain, "status">>;

export interface StaticDomain extends Domain {
    type: StaticDomainType;
}

// Backward compatibility interface for static domains (especially lobby domains)
export interface StaticDomainBackwardCompatible extends StaticDomain, DomainBackwardCompatible {
}

export type CreateStaticDomain = Omit<StaticDomain, "status" | "type"> & Partial<Pick<StaticDomain, "status" | "type">>;
