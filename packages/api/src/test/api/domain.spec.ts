import { expect } from "chai";
import { Application } from "express";
import { complexStructure, createComplexStructure, sortElements, truncate } from "../entities/helper";
import getUserService from "../../skywind/services/user/user";
import * as RoleService from "../../skywind/services/role";
import { BaseEntity } from "../../skywind/entities/entity";
import { application } from "../../skywind/server";
import { publicId } from "@skywind-group/sw-utils";
import { getDynamicDomainService, getStaticDomainService } from "../../skywind/services/domain";

const request = require("supertest");
const uuid = require("uuid");

const fixtures = [
    {
        domain: "someserver.skywindgroup.com",
        environment: "gc",
        description: "Test server for gaming",
        status: "active"
    },
    {
        domain: "qwerty.skywindgroup.com",
        environment: "nc",
        description: "Network controller server",
        status: "suspended"
    },
    {
        domain: "ytrewq.skywindgroup.com",
        environment: "fc",
        description: "File controller server",
        status: "active"
    }
];

describe("Domain API", () => {
    let server: Application;
    let master: BaseEntity;
    let accessToken: string;

    const serverPath = "/v1/";
    const getPath = (path: string) => serverPath + path;

    const timestamp = new Date().toJSON()
        .substring(0, 19)
        .replace(/:/g, "-");

    const getUniqueName = (name: string) => {
        return name + "_" + timestamp + "_" + uuid.v4().substr(0, 8);
    };

    const secretKey = complexStructure.masterKey;
    const username = getUniqueName("USER_");
    const password = "password_" + uuid.v4();

    const makeLogin = async () => {
        const response = await request(server)
            .post("/v1/login")
            .send({
                secretKey: secretKey,
                username: username,
                password: password,
            });

        expect(response.status).to.be.equal(200);
        expect(response.body.username).to.be.equal(username);
        accessToken = response.body.accessToken;
    };

    const createDomains = async (domains) => {
        for (const domain of domains) {
            await getDynamicDomainService()
                .create(domain); // TODO: add Promise all

            await getStaticDomainService()
                .create(domain); // TODO: add Promise all
        }
    };

    before(async () => {
        server = await application.get();
    });

    beforeEach(async () => {
        await truncate();

        master = await createComplexStructure();

        const role = await RoleService.createRole({
            title: "AUDITOR",
            permissions: [
                "domain",
                "keyentity:entitydomain"
            ],
            entityId: master.id,
            isShared: false
        });

        const userService = getUserService(master);
        await userService.create({
            username: username,
            password: password,
            roles: [role.toInfo()],
            email: "<EMAIL>",
        });

        await createDomains(fixtures);
        await makeLogin();
    });

    describe("POST /domains", () => {
        it("Should create dynamic domains", async () => {
            const response = await request(server)
                .post(getPath("domains/dynamic/"))
                .set("x-access-token", accessToken)
                .send({
                    domain: "qazwsx.skywindgroup.com",
                    environment: "gc"
                });

            expect(response.status).to.be.equal(201);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;
            const expectedDomain = {
                domain: "qazwsx.skywindgroup.com",
                environment: "gc",
                status: "active"
            };

            expect(receivedDomain).to.have.deep.property("domain", expectedDomain.domain);
            expect(receivedDomain).to.have.deep.property("environment", expectedDomain.environment);
            expect(receivedDomain).to.have.deep.property("status", expectedDomain.status);
        });
    });

    describe("GET /domains", () => {
        it("Should return dynamic domains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);

            const receivedDomains = sortElements(response.body, "domain");
            const expectedDomains = sortElements(fixtures, "domain");

            receivedDomains.forEach((receivedDomain, idx) => {
                const { domain } = expectedDomains[idx];

                expect(receivedDomain).to.have.deep.property("domain", domain);
            });
        });

        it("Should return static domains", async () => {
            const response = await request(server)
                .get(getPath("domains/static/"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);

            const receivedDomains = sortElements(response.body, "domain");
            const expectedDomains = sortElements(fixtures, "domain");

            receivedDomains.forEach((receivedDomain, idx) => {
                const { domain } = expectedDomains[idx];

                expect(receivedDomain).to.have.deep.property("domain", domain);
            });
        });

        it("Should filter dynamic domains by environment", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?environment=gc"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);
            expect(response.body[0]).to.have.deep.property("environment", "gc");
            expect(response.body[0]).to.have.deep.property("domain", "someserver.skywindgroup.com");
        });

        it("Should filter dynamic domains by status", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?status=suspended"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);
            expect(response.body[0]).to.have.deep.property("status", "suspended");
            expect(response.body[0]).to.have.deep.property("domain", "qwerty.skywindgroup.com");
        });

        it("Should filter dynamic domains by domain name contains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?domain__contains=qwerty"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);
            expect(response.body[0]).to.have.deep.property("domain", "qwerty.skywindgroup.com");
        });

        it("Should filter dynamic domains by description contains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?description__contains=controller"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(2);

            // Both "Network controller server" and "File controller server" should match
            const descriptions = response.body.map(domain => domain.description).sort();
            expect(descriptions).to.deep.equal([
                "File controller server",
                "Network controller server"
            ]);
        });

        it("Should filter static domains by status", async () => {
            const response = await request(server)
                .get(getPath("domains/static/?status=active"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(2);
            response.body.forEach(domain => {
                expect(domain).to.have.deep.property("status", "active");
            });
        });

        it("Should filter static domains by domain name contains", async () => {
            const response = await request(server)
                .get(getPath("domains/static/?domain__contains=someserver"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);
            expect(response.body[0]).to.have.deep.property("domain", "someserver.skywindgroup.com");
        });

        it("Should combine multiple filters with AND logic for dynamic domains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?environment=gc&status=active"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);
            expect(response.body[0]).to.have.deep.property("environment", "gc");
            expect(response.body[0]).to.have.deep.property("status", "active");
            expect(response.body[0]).to.have.deep.property("domain", "someserver.skywindgroup.com");
        });

        it("Should support pagination for dynamic domains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?limit=1&offset=0"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);

            // Check pagination headers
            expect(response.headers["x-paging-total"]).to.exist;
            expect(response.headers["x-paging-limit"]).to.equal("1");
            expect(response.headers["x-paging-offset"]).to.equal("0");
        });

        it("Should support pagination for static domains", async () => {
            const response = await request(server)
                .get(getPath("domains/static/?limit=1&offset=0"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(1);

            // Check pagination headers
            expect(response.headers["x-paging-total"]).to.exist;
            expect(response.headers["x-paging-limit"]).to.equal("1");
            expect(response.headers["x-paging-offset"]).to.equal("0");
        });

        it("Should validate pagination parameters for dynamic domains", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?limit=-1"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(400);
        });

        it("Should validate pagination parameters for static domains", async () => {
            const response = await request(server)
                .get(getPath("domains/static/?offset=-1"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(400);
        });

        it("Should return empty array when no domains match filter", async () => {
            const response = await request(server)
                .get(getPath("domains/dynamic/?environment=nonexistent"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);
            expect(response.body).to.have.lengthOf(0);
        });
    });

    describe("PATCH /domains", () => {
        it("Should update dynamic domain", async () => {
            const dynamicDomainIdToUpdate = publicId.instance.encode(1);

            const response = await request(server)
                .patch(getPath(`domains/dynamic/${dynamicDomainIdToUpdate}`))
                .set("x-access-token", accessToken)
                .send({
                    domain: "another.domain.com",
                    environment: "lc"
                });

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;
            const expectedDomain = {
                domain: "another.domain.com",
                environment: "lc"
            };

            expect(receivedDomain).to.have.deep.property("domain", expectedDomain.domain);
            expect(receivedDomain).to.have.deep.property("environment", expectedDomain.environment);
        });

        it("Should update static domain", async () => {
            const staticDomainIdToUpdate = publicId.instance.encode(1);

            const response = await request(server)
                .patch(getPath(`domains/static/${staticDomainIdToUpdate}`))
                .set("x-access-token", accessToken)
                .send({
                    domain: "another.domain.com",
                });

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;
            const expectedDomain = {
                domain: "another.domain.com"
            };

            expect(receivedDomain).to.have.deep.property("domain", expectedDomain.domain);
        });
    });

    describe("DELETE /domains", () => {
        it("Should delete dynamic domain", async () => {
            const dynamicDomainIdToDelete = publicId.instance.encode(1);

            const response = await request(server)
                .delete(getPath(`domains/dynamic/${dynamicDomainIdToDelete}`))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(204);
        });

        it("Should delete static domain", async () => {
            const staticDomainIdToDelete = publicId.instance.encode(1);

            const response = await request(server)
                .delete(getPath(`domains/static/${staticDomainIdToDelete}`))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(204);
        });
    });

    describe("PUT /entitydomain", () => {
        it("Should set dynamic entity domain", async () => {
            const dynamicDomainIdToSet = publicId.instance.encode(1);

            const response = await request(server)
                .put(getPath(`entitydomain/dynamic/${dynamicDomainIdToSet}`))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
        });

        it("Should set static entity domain", async () => {
            const staticDomainIdToSet = publicId.instance.encode(1);

            const response = await request(server)
                .put(getPath(`entitydomain/static/${staticDomainIdToSet}`))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
        });
    });

    describe("DELETE /entitydomain", () => {
        it("Should reset dynamic entity domain", async () => {
            const response = await request(server)
                .delete(getPath("entitydomain/dynamic/"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(204);
        });

        it("Should reset static entity domain", async () => {
            const response = await request(server)
                .delete(getPath("entitydomain/static/"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(204);
        });
    });

    describe("Lobby Domain Backward Compatibility", () => {
        let createdLobbyDomainId: number;

        it("Should create lobby domain with backward compatibility fields", async () => {
            const response = await request(server)
                .post(getPath("domains/lobby/"))
                .set("x-access-token", accessToken)
                .send({
                    name: "lobby-test.skywindgroup.com", // using 'name' instead of 'domain'
                    isActive: true, // using 'isActive' instead of 'status'
                    description: "Test lobby domain for backward compatibility"
                });

            expect(response.status).to.be.equal(201);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;
            createdLobbyDomainId = receivedDomain.id;

            // Check that both old and new fields are present
            expect(receivedDomain).to.have.property("domain", "lobby-test.skywindgroup.com");
            expect(receivedDomain).to.have.property("name", "lobby-test.skywindgroup.com");
            expect(receivedDomain).to.have.property("status", "active");
            expect(receivedDomain).to.have.property("isActive", true);
            expect(receivedDomain).to.have.property("type", "lobby");
        });

        it("Should get lobby domain with backward compatibility fields", async () => {
            const response = await request(server)
                .get(getPath(`domains/lobby/${createdLobbyDomainId}`))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;

            // Check that both old and new fields are present
            expect(receivedDomain).to.have.property("domain", "lobby-test.skywindgroup.com");
            expect(receivedDomain).to.have.property("name", "lobby-test.skywindgroup.com");
            expect(receivedDomain).to.have.property("status", "active");
            expect(receivedDomain).to.have.property("isActive", true);
            expect(receivedDomain).to.have.property("type", "lobby");
        });

        it("Should update lobby domain using backward compatibility fields", async () => {
            const response = await request(server)
                .patch(getPath(`domains/lobby/${createdLobbyDomainId}`))
                .set("x-access-token", accessToken)
                .send({
                    name: "updated-lobby.skywindgroup.com", // using 'name' instead of 'domain'
                    isActive: false // using 'isActive' instead of 'status'
                });

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Object);

            const receivedDomain = response.body;

            // Check that both old and new fields are present and updated
            expect(receivedDomain).to.have.property("domain", "updated-lobby.skywindgroup.com");
            expect(receivedDomain).to.have.property("name", "updated-lobby.skywindgroup.com");
            expect(receivedDomain).to.have.property("status", "suspended");
            expect(receivedDomain).to.have.property("isActive", false);
            expect(receivedDomain).to.have.property("type", "lobby");
        });

        it("Should list lobby domains with backward compatibility fields", async () => {
            const response = await request(server)
                .get(getPath("domains/lobby/"))
                .set("x-access-token", accessToken);

            expect(response.status).to.be.equal(200);
            expect(response.body).to.be.instanceof(Array);

            if (response.body.length > 0) {
                const domain = response.body[0];
                // Check that both old and new fields are present
                expect(domain).to.have.property("domain");
                expect(domain).to.have.property("name");
                expect(domain).to.have.property("status");
                expect(domain).to.have.property("isActive");
                expect(domain).to.have.property("type", "lobby");

                // Verify the mapping is correct
                expect(domain.name).to.equal(domain.domain);
                expect(domain.isActive).to.equal(domain.status === "active");
            }
        });
    });
});
